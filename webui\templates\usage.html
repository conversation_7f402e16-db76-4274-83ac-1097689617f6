<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Usage Statistics - LiteLLM</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Crimson+Pro:wght@300;400;600;700&family=Inter:wght@300;400;500;600&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: url('https://unsplash.com/photos/cPa-7yByq3o/download?ixid=M3wxMjA3fDB8MXxhbGx8fHx8fHx8fHwxNzYyMjU1NTAzfA&force=true&w=2400') center center / cover no-repeat, linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            padding: 40px 20px;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 80%, rgba(74, 86, 226, 0.1) 0%, transparent 50%);
            pointer-events: none;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(40px) saturate(180%);
            -webkit-backdrop-filter: blur(40px) saturate(180%);
            border-radius: 24px;
            padding: 48px;
            box-shadow: 
                0 8px 32px 0 rgba(0, 0, 0, 0.37),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.08);
            animation: containerFadeIn 0.6s ease-out;
        }

        @keyframes containerFadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 32px;
            color: rgba(120, 119, 198, 0.9);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            padding: 10px 18px;
            background: rgba(120, 119, 198, 0.1);
            border-radius: 12px;
            border: 1px solid rgba(120, 119, 198, 0.2);
            transition: all 0.3s ease;
        }

        .back-link:hover {
            background: rgba(120, 119, 198, 0.15);
            border-color: rgba(120, 119, 198, 0.3);
            transform: translateX(-4px);
        }

        h1 {
            font-family: 'Crimson Pro', serif;
            color: rgba(255, 255, 255, 0.95);
            margin-bottom: 12px;
            font-size: 42px;
            font-weight: 600;
            letter-spacing: -1px;
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.5);
            font-size: 15px;
            margin-bottom: 40px;
        }

        .user-input {
            margin-bottom: 48px;
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .user-input input {
            padding: 16px 24px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            font-size: 15px;
            width: 320px;
            background: rgba(255, 255, 255, 0.03);
            color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            font-family: 'Inter', sans-serif;
        }

        .user-input input:focus {
            outline: none;
            border-color: rgba(120, 119, 198, 0.5);
            background: rgba(255, 255, 255, 0.05);
            box-shadow: 0 8px 32px rgba(120, 119, 198, 0.2);
        }

        .user-input input::placeholder {
            color: rgba(255, 255, 255, 0.3);
        }

        .user-input button {
            padding: 16px 36px;
            background: rgba(120, 119, 198, 0.25);
            color: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(120, 119, 198, 0.3);
            border-radius: 16px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Inter', sans-serif;
        }

        .user-input button:hover {
            background: rgba(120, 119, 198, 0.35);
            border-color: rgba(120, 119, 198, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(120, 119, 198, 0.3);
        }

        .user-input button:active {
            transform: translateY(0);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 20px;
            margin-bottom: 56px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.08);
            padding: 28px;
            border-radius: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            animation: cardFadeIn 0.5s ease-out backwards;
        }

        .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .stat-card:nth-child(2) { animation-delay: 0.15s; }
        .stat-card:nth-child(3) { animation-delay: 0.2s; }
        .stat-card:nth-child(4) { animation-delay: 0.25s; }
        .stat-card:nth-child(5) { animation-delay: 0.3s; }
        .stat-card:nth-child(6) { animation-delay: 0.35s; }

        @keyframes cardFadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, rgba(120, 119, 198, 0.8), rgba(74, 86, 226, 0.8));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(120, 119, 198, 0.3);
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(120, 119, 198, 0.2);
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        .stat-card h3 {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1.2px;
            font-weight: 600;
        }

        .stat-card .value {
            font-family: 'Crimson Pro', serif;
            font-size: 36px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.95);
            letter-spacing: -0.5px;
        }

        .section {
            margin-bottom: 56px;
            animation: sectionFadeIn 0.6s ease-out;
        }

        @keyframes sectionFadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .section h2 {
            font-family: 'Crimson Pro', serif;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.5px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.06);
        }

        th {
            background: rgba(255, 255, 255, 0.03);
            padding: 18px 20px;
            text-align: left;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.6);
            text-transform: uppercase;
            font-size: 11px;
            letter-spacing: 1px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.06);
        }

        td {
            padding: 18px 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.04);
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        tr {
            transition: background 0.2s ease;
        }

        tbody tr:hover {
            background: rgba(255, 255, 255, 0.03);
        }

        .loading {
            text-align: center;
            padding: 80px 40px;
            color: rgba(255, 255, 255, 0.4);
            font-size: 15px;
        }

        .loading::before {
            content: '';
            display: inline-block;
            width: 40px;
            height: 40px;
            margin-bottom: 16px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-top-color: rgba(120, 119, 198, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error {
            background: rgba(248, 113, 113, 0.15);
            color: rgba(248, 113, 113, 0.9);
            padding: 24px 28px;
            border-radius: 16px;
            border: 1px solid rgba(248, 113, 113, 0.3);
            border-left: 3px solid rgba(248, 113, 113, 0.6);
            font-size: 15px;
            animation: errorShake 0.5s ease-out;
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-8px); }
            75% { transform: translateX(8px); }
        }

        .no-data {
            text-align: center;
            padding: 80px 40px;
            color: rgba(255, 255, 255, 0.4);
            font-size: 15px;
        }

        .request-item {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(20px);
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(255, 255, 255, 0.06);
            border-left: 3px solid rgba(120, 119, 198, 0.6);
            transition: all 0.3s ease;
            animation: requestFadeIn 0.4s ease-out backwards;
        }

        .request-item:nth-child(1) { animation-delay: 0.05s; }
        .request-item:nth-child(2) { animation-delay: 0.1s; }
        .request-item:nth-child(3) { animation-delay: 0.15s; }
        .request-item:nth-child(4) { animation-delay: 0.2s; }
        .request-item:nth-child(5) { animation-delay: 0.25s; }

        @keyframes requestFadeIn {
            from { opacity: 0; transform: translateX(-20px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .request-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
            transform: translateX(4px);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.4);
            font-weight: 500;
        }

        .request-query {
            font-family: 'Crimson Pro', serif;
            font-weight: 600;
            margin-bottom: 10px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            line-height: 1.5;
        }

        .request-response {
            font-family: 'Crimson Pro', serif;
            color: rgba(255, 255, 255, 0.7);
            font-size: 15px;
            margin-bottom: 14px;
            line-height: 1.6;
        }

        .request-meta {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.4);
            flex-wrap: wrap;
        }

        .request-meta span {
            padding: 4px 10px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 6px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-link">← Back to Chat</a>
        
        <h1>Usage Statistics</h1>
        <div class="subtitle">Monitor your LiteLLM API usage and performance metrics</div>
        
        <div class="user-input">
            <input type="text" id="userIdInput" placeholder="Enter User ID" value="demo-user">
            <button onclick="loadUserStats()">Load Stats</button>
        </div>

        <div id="content">
            <div class="loading">Enter a User ID and click "Load Stats"</div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;

        async function loadUserStats() {
            const userId = document.getElementById('userIdInput').value.trim();
            if (!userId) {
                alert('Please enter a User ID');
                return;
            }

            const contentDiv = document.getElementById('content');
            contentDiv.innerHTML = '<div class="loading">Loading statistics...</div>';

            try {
                const response = await fetch(`${API_BASE}/api/proxy/user/${userId}/usage`);
                const data = await response.json();

                if (data.error) {
                    contentDiv.innerHTML = `<div class="error">⚠️ Error: ${data.error}</div>`;
                    return;
                }

                renderStats(data);
            } catch (error) {
                contentDiv.innerHTML = `<div class="error">⚠️ Failed to load statistics: ${error.message}</div>`;
            }
        }

        function renderStats(data) {
            const summary = data.summary;
            const contentDiv = document.getElementById('content');

            let html = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Requests</h3>
                        <div class="value">${summary.total_requests}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Tokens</h3>
                        <div class="value">${summary.total_tokens.toLocaleString()}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Cost</h3>
                        <div class="value">$${summary.total_cost_usd.toFixed(4)}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Avg Response Time</h3>
                        <div class="value">${summary.avg_response_time.toFixed(2)}s</div>
                    </div>
                    <div class="stat-card">
                        <h3>Apps Used</h3>
                        <div class="value">${summary.apps_used}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Models Used</h3>
                        <div class="value">${summary.models_used}</div>
                    </div>
                </div>
            `;

            // Usage by Application
            if (data.usage_by_application && data.usage_by_application.length > 0) {
                html += `
                    <div class="section">
                        <h2>Usage by Application</h2>
                        <table>
                            <thead>
                                <tr>
                                    <th>Application</th>
                                    <th>Requests</th>
                                    <th>Tokens</th>
                                    <th>Cost</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                data.usage_by_application.forEach(app => {
                    html += `
                        <tr>
                            <td>${app.application_id}</td>
                            <td>${app.request_count}</td>
                            <td>${(app.tokens || 0).toLocaleString()}</td>
                            <td>$${(app.cost || 0).toFixed(6)}</td>
                        </tr>
                    `;
                });
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // Usage by Model
            if (data.usage_by_model && data.usage_by_model.length > 0) {
                html += `
                    <div class="section">
                        <h2>Usage by Model</h2>
                        <table>
                            <thead>
                                <tr>
                                    <th>Model</th>
                                    <th>Requests</th>
                                    <th>Tokens</th>
                                    <th>Cost</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                data.usage_by_model.forEach(model => {
                    html += `
                        <tr>
                            <td>${model.model}</td>
                            <td>${model.request_count}</td>
                            <td>${(model.tokens || 0).toLocaleString()}</td>
                            <td>$${(model.cost || 0).toFixed(6)}</td>
                        </tr>
                    `;
                });
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            }

            // Recent Requests
            if (data.recent_requests && data.recent_requests.length > 0) {
                html += `
                    <div class="section">
                        <h2>Recent Requests (Last 50)</h2>
                `;
                data.recent_requests.forEach(req => {
                    const timestamp = new Date(req.timestamp).toLocaleString();
                    html += `
                        <div class="request-item">
                            <div class="request-header">
                                <span>🕐 ${timestamp}</span>
                                <span>📱 ${req.application_id}</span>
                            </div>
                            <div class="request-query"><strong>Q:</strong> ${escapeHtml(req.query)}</div>
                            ${req.response ? `<div class="request-response"><strong>A:</strong> ${escapeHtml(req.response.substring(0, 200))}${req.response.length > 200 ? '...' : ''}</div>` : ''}
                            <div class="request-meta">
                                <span>🤖 ${req.model}</span>
                                <span>⏱️ ${req.response_time}s</span>
                                <span>📊 ${req.total_tokens} tokens</span>
                                <span>💰 $${(req.cost_usd || 0).toFixed(6)}</span>
                                <span>✅ ${req.status}</span>
                            </div>
                        </div>
                    `;
                });
                html += `</div>`;
            } else {
                html += '<div class="no-data">No requests found for this user.</div>';
            }

            contentDiv.innerHTML = html;
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Load stats on page load if user ID is in URL
        window.addEventListener('DOMContentLoaded', () => {
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('user_id');
            if (userId) {
                document.getElementById('userIdInput').value = userId;
                loadUserStats();
            }
        });
    </script>
</body>
</html>