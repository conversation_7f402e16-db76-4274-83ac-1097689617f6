{"cells": [{"cell_type": "markdown", "id": "6afbd393", "metadata": {}, "source": ["## OpenAI\n", "- https://platform.openai.com/docs/overview\n", "- https://platform.openai.com/settings/organization/api-keys"]}, {"cell_type": "code", "execution_count": 1, "id": "eb81b056", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "from openai import OpenAI\n", "\n", "load_dotenv()  # Load environment variables from .env file\n", "client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))"]}, {"cell_type": "code", "execution_count": 2, "id": "d2475739", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The capital of **Nepal** is **Kathmandu**.\n"]}], "source": ["response = client.responses.create(\n", "    model=\"gpt-4.1\",\n", "    input=\"What is the capital of Nepal?.\"\n", ")\n", "\n", "print(response.output_text)"]}, {"cell_type": "code", "execution_count": 3, "id": "c1245762", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Response(id='resp_0c31410f8045688d00690856759da08190a178f4fa73b91a92', created_at=1762154101.0, error=None, incomplete_details=None, instructions=None, metadata={}, model='gpt-4.1-2025-04-14', object='response', output=[ResponseOutputMessage(id='msg_0c31410f8045688d0069085678660c8190a03a919fb98aee9f', content=[ResponseOutputText(annotations=[], text='The capital of **Nepal** is **Kathmandu**.', type='output_text', logprobs=[])], role='assistant', status='completed', type='message')], parallel_tool_calls=True, temperature=1.0, tool_choice='auto', tools=[], top_p=1.0, background=False, max_output_tokens=None, previous_response_id=None, reasoning=Reasoning(effort=None, generate_summary=None, summary=None), service_tier='default', status='completed', text=ResponseTextConfig(format=ResponseFormatText(type='text'), verbosity='medium'), truncation='disabled', usage=ResponseUsage(input_tokens=14, input_tokens_details=InputTokensDetails(cached_tokens=0), output_tokens=14, output_tokens_details=OutputTokensDetails(reasoning_tokens=0), total_tokens=28), user=None, billing={'payer': 'developer'}, max_tool_calls=None, prompt_cache_key=None, prompt_cache_retention=None, safety_identifier=None, store=True, top_logprobs=0)\n"]}], "source": ["print(response)"]}, {"cell_type": "code", "execution_count": 4, "id": "870f850c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"id\": \"resp_0c31410f8045688d00690856759da08190a178f4fa73b91a92\",\n", "  \"created_at\": 1762154101.0,\n", "  \"error\": null,\n", "  \"incomplete_details\": null,\n", "  \"instructions\": null,\n", "  \"metadata\": {},\n", "  \"model\": \"gpt-4.1-2025-04-14\",\n", "  \"object\": \"response\",\n", "  \"output\": [\n", "    {\n", "      \"id\": \"msg_0c31410f8045688d0069085678660c8190a03a919fb98aee9f\",\n", "      \"content\": [\n", "        {\n", "          \"annotations\": [],\n", "          \"text\": \"The capital of **Nepal** is **Kathmandu**.\",\n", "          \"type\": \"output_text\",\n", "          \"logprobs\": []\n", "        }\n", "      ],\n", "      \"role\": \"assistant\",\n", "      \"status\": \"completed\",\n", "      \"type\": \"message\"\n", "    }\n", "  ],\n", "  \"parallel_tool_calls\": true,\n", "  \"temperature\": 1.0,\n", "  \"tool_choice\": \"auto\",\n", "  \"tools\": [],\n", "  \"top_p\": 1.0,\n", "  \"background\": false,\n", "  \"max_output_tokens\": null,\n", "  \"previous_response_id\": null,\n", "  \"reasoning\": {\n", "    \"effort\": null,\n", "    \"generate_summary\": null,\n", "    \"summary\": null\n", "  },\n", "  \"service_tier\": \"default\",\n", "  \"status\": \"completed\",\n", "  \"text\": {\n", "    \"format\": {\n", "      \"type\": \"text\"\n", "    },\n", "    \"verbosity\": \"medium\"\n", "  },\n", "  \"truncation\": \"disabled\",\n", "  \"usage\": {\n", "    \"input_tokens\": 14,\n", "    \"input_tokens_details\": {\n", "      \"cached_tokens\": 0\n", "    },\n", "    \"output_tokens\": 14,\n", "    \"output_tokens_details\": {\n", "      \"reasoning_tokens\": 0\n", "    },\n", "    \"total_tokens\": 28\n", "  },\n", "  \"user\": null,\n", "  \"billing\": {\n", "    \"payer\": \"developer\"\n", "  },\n", "  \"max_tool_calls\": null,\n", "  \"prompt_cache_key\": null,\n", "  \"prompt_cache_retention\": null,\n", "  \"safety_identifier\": null,\n", "  \"store\": true,\n", "  \"top_logprobs\": 0\n", "}\n"]}], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "markdown", "id": "dbc91def", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>\n", "- https://github.com/ollama/ollama-python"]}, {"cell_type": "code", "execution_count": null, "id": "7564b70a", "metadata": {}, "outputs": [], "source": ["from ollama import chat\n", "from ollama import ChatResponse\n", "\n", "response: ChatResponse = chat(model='llama3', messages=[\n", "  {\n", "    'role': 'user',\n", "    'content': 'Capital of Nepal?',\n", "  },\n", "])\n", "print(response['message']['content'])\n", "# or access fields directly from the response object\n", "print(response.message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "94a3843d", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "markdown", "id": "22100787", "metadata": {}, "source": ["## Groq\n", "- https://console.groq.com/docs/quickstart\n", "- https://console.groq.com/keys"]}, {"cell_type": "code", "execution_count": null, "id": "6b1e2c46", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from groq import Groq\n", "\n", "client = Groq(\n", "    api_key=os.getenv(\"GROQ_API_KEY\"),\n", ")\n", "\n", "chat_completion = client.chat.completions.create(\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What is the capital of Nepal?\",\n", "        }\n", "    ],\n", "    model=\"llama3-8b-8192\",\n", ")\n", "\n", "print(chat_completion.choices[0].message.content)\n", "print(chat_completion)"]}, {"cell_type": "code", "execution_count": null, "id": "23ec78b7", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = chat_completion.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "markdown", "id": "5b8d6919", "metadata": {}, "source": ["## What happens with LiteLLM ??\n", "- https://docs.litellm.ai/docs/"]}, {"cell_type": "code", "execution_count": null, "id": "f61cc7d2", "metadata": {}, "outputs": [], "source": ["from litellm import completion"]}, {"cell_type": "code", "execution_count": null, "id": "5a16b9de", "metadata": {}, "outputs": [], "source": ["completion??"]}, {"cell_type": "code", "execution_count": null, "id": "576c1174", "metadata": {}, "outputs": [], "source": ["from litellm import completion\n", "import os\n", "\n", "## set ENV variables\n", "os.environ[\"OPENAI_API_KEY\"] = os.getenv('OPENAI_API_KEY')\n", "\n", "response = completion(\n", "  model=\"openai/gpt-4.1\",\n", "  messages=[{ \"content\": \"What is the capital of Nepal\",\"role\": \"user\"}]\n", ")\n", "\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "a6f3ff97", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "id": "f7277ea4", "metadata": {}, "outputs": [], "source": ["from litellm import completion\n", "\n", "response = completion(\n", "            model=\"ollama/llama3\",\n", "            messages = [{ \"content\": \"What is the capital of Nepal?\",\"role\": \"user\"}],\n", "            api_base=\"http://localhost:11434\"\n", ")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "4497d8cf", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "id": "f924a7c6", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()  # Load environment variables from .env file\n", "os.environ['GROQ_API_KEY'] = os.getenv(\"GROQ_API_KEY\")\n", "\n", "response = completion(\n", "    model=\"groq/llama3-8b-8192\", \n", "    messages=[\n", "       {\"role\": \"user\", \"content\": \"What is the capital of Nepal?\"}\n", "   ],\n", ")\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "39003d2c", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "markdown", "id": "e861ecec", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> with <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "2d5d46f2", "metadata": {}, "outputs": [], "source": ["import litellm\n", "\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()  # Load environment variables from .env file\n", "\n", "os.environ[\"LANGSMITH_API_KEY\"] = os.getenv('LANGSMITH_API_KEY')\n", "os.environ[\"LANGSMITH_PROJECT\"] = \"\" # defaults to litellm-completion\n", "os.environ[\"LANGSMITH_DEFAULT_RUN_NAME\"] = \"\" # defaults to LLMRun\n", "\n", "\n", "# set langsmith as a callback, litellm will send the data to langsmith\n", "litellm.callbacks = [\"langsmith\"] \n", " \n", "from ollama import chat\n", "from ollama import ChatResponse\n", "\n", "response: ChatResponse = chat(model='llama3', messages=[\n", "  {\n", "    'role': 'user',\n", "    'content': 'Capital of Nepal?',\n", "  },\n", "])\n", "print(response['message']['content'])\n", "# or access fields directly from the response object\n", "print(response.message.content)"]}, {"cell_type": "markdown", "id": "90368ffc", "metadata": {}, "source": ["##### Didn't work with <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "be88e53f", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()  # Load environment variables from .env file\n", "os.environ['GROQ_API_KEY'] = os.getenv(\"GROQ_API_KEY\")\n", "\n", "response = completion(\n", "    model=\"groq/llama3-8b-8192\", \n", "    messages=[\n", "       {\"role\": \"user\", \"content\": \"What is the capital of Nepal?\"}\n", "   ],\n", ")\n", "print(response)"]}, {"cell_type": "markdown", "id": "d84d8818", "metadata": {}, "source": ["### Litellm Proxy via python code"]}, {"cell_type": "code", "execution_count": null, "id": "7360110d", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()  # Load environment variables from .env file\n", "\n", "url = 'http://0.0.0.0:4000/chat/completions'\n", "headers = {\n", "    'Content-Type': 'application/json',\n", "    'Authorization': f'Bearer {os.getenv(\"LITELLM_PROXY_API_KEY\")}'\n", "}\n", "data = {\n", "    \"model\": \"gpt-4.1-mini\",\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What is the capital of Nepal ?\"\n", "        }\n", "    ]\n", "}\n", "\n", "response = requests.post(url, headers=headers, json=data)\n", "print(json.dumps(response.json(), indent=2))"]}, {"cell_type": "markdown", "id": "ce3375cf", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "e97fb857", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()  # Load environment variables from .env file\n", "\n", "url = 'http://0.0.0.0:4000/chat/completions'\n", "headers = {\n", "    'Content-Type': 'application/json',\n", "    'Authorization': f'Bearer {os.getenv(\"LITELLM_PROXY_API_KEY\")}'\n", "}\n", "data = {\n", "    \"model\": \"groq/qwen-qwq-32b\",\n", "    \"messages\": [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What is the capital of Nepal ?\"\n", "        }\n", "    ]\n", "}\n", "\n", "response = requests.post(url, headers=headers, json=data)\n", "print(json.dumps(response.json(), indent=2))"]}, {"cell_type": "markdown", "id": "4452d894", "metadata": {}, "source": ["### Proxy via Litellm sdk"]}, {"cell_type": "code", "execution_count": null, "id": "34997a95", "metadata": {}, "outputs": [], "source": ["from litellm import completion\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()  # Load environment variables from .env file\n", "\n", "LITELLM_PROXY_API_KEY = os.getenv('LITELLM_PROXY_API_KEY')\n", "\n", "response = completion(\n", "    model=\"gpt-4.1-mini\",\n", "    messages=[{\"role\": \"user\", \"content\": \"What is the capital of Nepal ?\"}],\n", "    api_base=\"http://localhost:4000\",  # Base URL of the LiteLLM Proxy Server\n", "    api_key=LITELLM_PROXY_API_KEY  # API key for the LiteLLM Proxy Server\n", ")\n", "\n", "print(response['choices'][0]['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "70710236", "metadata": {}, "outputs": [], "source": ["print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "d785d70a", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}, {"cell_type": "code", "execution_count": null, "id": "4436a0d5", "metadata": {}, "outputs": [], "source": ["from litellm import completion\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()  # Load environment variables from .env file\n", "\n", "LITELLM_PROXY_API_KEY = os.getenv('LITELLM_PROXY_API_KEY')\n", "\n", "response = completion(\n", "    model=\"groq/qwen-qwq-32b\",\n", "    messages=[{\"role\": \"user\", \"content\": \"What is the capital of Nepal ?\"}],\n", "    api_base=\"http://localhost:4000\",  # Base URL of the LiteLLM Proxy Server\n", "    api_key=LITELLM_PROXY_API_KEY  # API key for the LiteLLM Proxy Server\n", ")\n", "\n", "print(response['choices'][0]['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "9a5b4a06", "metadata": {}, "outputs": [], "source": ["#https://docs.litellm.ai/docs/providers/openai_compatible\n", "\n", "from litellm import completion\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()  # Load environment variables from .env file\n", "\n", "LITELLM_PROXY_API_KEY = os.getenv('LITELLM_PROXY_API_KEY')\n", "\n", "response = completion(\n", "    model=\"openai/groq/qwen-qwq-32b\",\n", "    messages=[{\"role\": \"user\", \"content\": \"What is the capital of Nepal ?\"}],\n", "    api_base=\"http://localhost:4000\",  # Base URL of the LiteLLM Proxy Server\n", "    api_key=LITELLM_PROXY_API_KEY  # API key for the LiteLLM Proxy Server\n", ")\n", "\n", "print(response['choices'][0]['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "aa0f4059", "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "# Convert response to dictionary and print with formatting\n", "response_dict = response.model_dump()\n", "print(json.dumps(response_dict, indent=2, ensure_ascii=False))"]}], "metadata": {"kernelspec": {"display_name": "litellm-crash-course", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.14"}}, "nbformat": 4, "nbformat_minor": 5}