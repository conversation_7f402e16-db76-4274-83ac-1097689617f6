# ============================================================
# LiteLLM - API Keys Configuration
# ============================================================

# OpenAI - https://platform.openai.com/api-keys
OPENAI_API_KEY="sk-proj-xxxxxxxx"

# Anthropic (Claude) - https://console.anthropic.com/settings/keys
ANTHROPIC_API_KEY="sk-ant-xxxxxxxx"

# Google Gemini - https://aistudio.google.com/apikey
GOOGLE_API_KEY="AIzaSyxxxxxxxx"

# Groq - https://console.groq.com/keys
GROQ_API_KEY="gsk_xxxxxxxx"

# Azure OpenAI - https://portal.azure.com
AZURE_API_KEY="xxxxxxxx"
AZURE_API_BASE="https://your-resource.openai.azure.com/"
AZURE_API_VERSION="2024-02-15-preview"

# Cohere - https://dashboard.cohere.com/api-keys
COHERE_API_KEY="xxxxxxxx"

# Mistral AI - https://console.mistral.ai/api-keys
MISTRAL_API_KEY="xxxxxxxx"

# Perplexity AI - https://www.perplexity.ai/settings/api
PERPLEXITYAI_API_KEY="pplx-xxxxxxxx"

# DeepSeek - https://platform.deepseek.com/api_keys
DEEPSEEK_API_KEY="sk-xxxxxxxx"

# xAI (Grok) - https://console.x.ai/
XAI_API_KEY="xai-xxxxxxxx"

# Together AI - https://api.together.xyz/settings/api-keys
TOGETHERAI_API_KEY="xxxxxxxx"

# Replicate - https://replicate.com/account/api-tokens
REPLICATE_API_KEY="r8_xxxxxxxx"

# Hugging Face - https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY="hf_xxxxxxxx"

# Anyscale - https://app.endpoints.anyscale.com/credentials
ANYSCALE_API_KEY="esecret_xxxxxxxx"

# OpenRouter - https://openrouter.ai/keys
OPENROUTER_API_KEY="sk-or-v1-xxxxxxxx"

# Fireworks AI - https://fireworks.ai/api-keys
FIREWORKS_API_KEY="xxxxxxxx"

# AI21 - https://studio.ai21.com/account/api-key
AI21_API_KEY="xxxxxxxx"

# AWS Bedrock
AWS_ACCESS_KEY_ID="AKIAxxxxxxxx"
AWS_SECRET_ACCESS_KEY="xxxxxxxx"
AWS_REGION_NAME="us-east-1"

# Google Cloud (Vertex AI)
GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account-key.json"
VERTEX_PROJECT="your-gcp-project-id"
VERTEX_LOCATION="us-central1"

# Ollama (Local)
OLLAMA_API_BASE="http://localhost:11434"

# LangSmith (Optional - for tracing)
LANGSMITH_API_KEY="lsv2_xxxxxx"
LANGSMITH_PROJECT="your-project-name"